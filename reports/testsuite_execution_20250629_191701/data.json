{"name": "UI Execution 29/06/2025, 19:18:12", "testCases": [{"name": "apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1360ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1215ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Take Screenshot: \"after_edit_link_click\"", "status": "passed", "duration": "625ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1668ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1255ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1604ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1099ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Take Screenshot: \"after_closing_health_app\"", "status": "passed", "duration": "1707ms", "action_id": "Screenshot", "screenshot_filename": "Screenshot.png", "report_screenshot": "Screenshot.png", "resolved_screenshot": "screenshots/Screenshot.png"}]}, {"name": "health2\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Launch app: com.apple.Health", "status": "passed", "duration": "1176ms", "action_id": "UppP3ZuqY6", "screenshot_filename": "UppP3ZuqY6.png", "report_screenshot": "UppP3ZuqY6.png", "resolved_screenshot": "screenshots/UppP3ZuqY6.png", "clean_action_id": "UppP3ZuqY6", "prefixed_action_id": "al_UppP3ZuqY6", "action_id_screenshot": "screenshots/UppP3ZuqY6.png"}, {"name": "Wait for 2 ms", "status": "passed", "duration": "2017ms", "action_id": "ag29wsBP24", "screenshot_filename": "ag29wsBP24.png", "report_screenshot": "ag29wsBP24.png", "resolved_screenshot": "screenshots/ag29wsBP24.png", "clean_action_id": "ag29wsBP24", "prefixed_action_id": "al_ag29wsBP24", "action_id_screenshot": "screenshots/ag29wsBP24.png"}, {"name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "status": "passed", "duration": "1237ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Click element: xpath=//XCUIElementTypeButton[@name=\"Done\"]", "status": "passed", "duration": "1624ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png", "action_id_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5017ms", "action_id": "KQXXIoR0Xa", "screenshot_filename": "KQXXIoR0Xa.png", "report_screenshot": "KQXXIoR0Xa.png", "resolved_screenshot": "screenshots/KQXXIoR0Xa.png", "clean_action_id": "KQXXIoR0Xa", "prefixed_action_id": "al_KQXXIoR0Xa", "action_id_screenshot": "screenshots/KQXXIoR0Xa.png"}, {"name": "Terminate app: com.apple.Health", "status": "passed", "duration": "1079ms", "action_id": "XCUIElemen2", "screenshot_filename": "XCUIElemen2.png", "report_screenshot": "XCUIElemen2.png", "resolved_screenshot": "screenshots/XCUIElemen2.png", "clean_action_id": "XCUIElemen2", "prefixed_action_id": "al_XCUIElemen2", "action_id_screenshot": "screenshots/XCUIElemen2.png"}]}], "passed": 2, "failed": 0, "skipped": 0, "status": "passed"}