<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/29/2025, 7:18:12 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">2</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 29/06/2025, 19:18:12
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="8 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 apple health
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            8 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1360ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1215ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Take Screenshot: "after_edit_link_click" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">625ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1668ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1255ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath= //XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1604ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">1099ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="Screenshot.png" data-action-id="Screenshot" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Take Screenshot: "after_closing_health_app" <span class="action-id-badge" title="Action ID: Screenshot">Screenshot</span>
                            </div>
                            <span class="test-step-duration">1707ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 health2
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="UppP3ZuqY6.png" data-action-id="UppP3ZuqY6" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Launch app: com.apple.Health <span class="action-id-badge" title="Action ID: UppP3ZuqY6">UppP3ZuqY6</span>
                            </div>
                            <span class="test-step-duration">1176ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="ag29wsBP24.png" data-action-id="ag29wsBP24" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 2 ms <span class="action-id-badge" title="Action ID: ag29wsBP24">ag29wsBP24</span>
                            </div>
                            <span class="test-step-duration">2017ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1237ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Click element: xpath=//XCUIElementTypeButton[@name="Done"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1624ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="KQXXIoR0Xa.png" data-action-id="KQXXIoR0Xa" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait for 5 ms <span class="action-id-badge" title="Action ID: KQXXIoR0Xa">KQXXIoR0Xa</span>
                            </div>
                            <span class="test-step-duration">5017ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="XCUIElemen2.png" data-action-id="XCUIElemen2" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.apple.Health <span class="action-id-badge" title="Action ID: XCUIElemen2">XCUIElemen2</span>
                            </div>
                            <span class="test-step-duration">1079ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 29/06/2025, 19:18:12","testCases":[{"name":"apple health\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            8 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1360ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1215ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Take Screenshot: \"after_edit_link_click\"","status":"passed","duration":"625ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1668ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1255ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1604ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1099ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Take Screenshot: \"after_closing_health_app\"","status":"passed","duration":"1707ms","action_id":"Screenshot","screenshot_filename":"Screenshot.png","report_screenshot":"Screenshot.png","resolved_screenshot":"screenshots/Screenshot.png","action_id_screenshot":"screenshots/Screenshot.png"}]},{"name":"health2\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"passed","steps":[{"name":"Launch app: com.apple.Health","status":"passed","duration":"1176ms","action_id":"UppP3ZuqY6","screenshot_filename":"UppP3ZuqY6.png","report_screenshot":"UppP3ZuqY6.png","resolved_screenshot":"screenshots/UppP3ZuqY6.png","clean_action_id":"UppP3ZuqY6","prefixed_action_id":"al_UppP3ZuqY6","action_id_screenshot":"screenshots/UppP3ZuqY6.png"},{"name":"Wait for 2 ms","status":"passed","duration":"2017ms","action_id":"ag29wsBP24","screenshot_filename":"ag29wsBP24.png","report_screenshot":"ag29wsBP24.png","resolved_screenshot":"screenshots/ag29wsBP24.png","clean_action_id":"ag29wsBP24","prefixed_action_id":"al_ag29wsBP24","action_id_screenshot":"screenshots/ag29wsBP24.png"},{"name":"Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]","status":"passed","duration":"1237ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Click element: xpath=//XCUIElementTypeButton[@name=\"Done\"]","status":"passed","duration":"1624ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait for 5 ms","status":"passed","duration":"5017ms","action_id":"KQXXIoR0Xa","screenshot_filename":"KQXXIoR0Xa.png","report_screenshot":"KQXXIoR0Xa.png","resolved_screenshot":"screenshots/KQXXIoR0Xa.png","clean_action_id":"KQXXIoR0Xa","prefixed_action_id":"al_KQXXIoR0Xa","action_id_screenshot":"screenshots/KQXXIoR0Xa.png"},{"name":"Terminate app: com.apple.Health","status":"passed","duration":"1079ms","action_id":"XCUIElemen2","screenshot_filename":"XCUIElemen2.png","report_screenshot":"XCUIElemen2.png","resolved_screenshot":"screenshots/XCUIElemen2.png","clean_action_id":"XCUIElemen2","prefixed_action_id":"al_XCUIElemen2","action_id_screenshot":"screenshots/XCUIElemen2.png"}]}],"passed":2,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["7MOUNxtPJz.png","EsFYQdDK3F.png","KQXXIoR0Xa.png","SaJtvXOGlT.png","To6rgFtm9R.png","UppP3ZuqY6.png","WaakzZc6xF.png","XCUIElemen.png","XCUIElemen2.png","after_closing_health_app.png","after_edit_link_click.png","ag29wsBP24.png","jF4jRny1iE.png","latest.png","oIAtyQB5wY.png","placeholder.png","xCb3tcOZW3.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>