import React from 'react';
import { Routes, Route } from 'react-router-dom';
import styled from 'styled-components';
import Header from './Header';
import Sidebar from './Sidebar';
import Dashboard from './Dashboard';
import DeviceList from './DeviceList';
import TestSuites from './TestSuites';
import TestExecution from './TestExecution';
import Results from './Results';
import Settings from './Settings';

const AppContainer = styled.div`
  display: flex;
  min-height: 100vh;
`;

const MainContent = styled.main`
  flex: 1;
  padding: 20px;
  background-color: #f5f5f5;
`;

function App() {
  return (
    <>
      <Header />
      <AppContainer>
        <Sidebar />
        <MainContent>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/devices" element={<DeviceList />} />
            <Route path="/test-suites" element={<TestSuites />} />
            <Route path="/execution" element={<TestExecution />} />
            <Route path="/results" element={<Results />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </MainContent>
      </AppContainer>
    </>
  );
}

export default App; 