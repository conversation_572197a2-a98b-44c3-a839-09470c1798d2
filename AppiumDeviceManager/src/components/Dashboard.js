import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const DashboardContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
`;

const StatCard = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
`;

const StatHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const StatTitle = styled.h3`
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 10px;
`;

const StatFooter = styled.div`
  font-size: 0.9rem;
  color: ${props => props.$isPositive ? '#27ae60' : '#e74c3c'};
  display: flex;
  align-items: center;
  margin-top: auto;
`;

const ActionButton = styled(Link)`
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  text-decoration: none;
  display: inline-block;
  margin-top: 10px;
  text-align: center;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: #2980b9;
  }
`;

const SectionTitle = styled.h2`
  margin-bottom: 20px;
  font-size: 1.5rem;
  color: #2c3e50;
`;

const RecentTests = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
`;

const TestList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const TestItem = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 15px;
  border-radius: 4px;
  background-color: #f8f9fa;
  align-items: center;
`;

const TestName = styled.div`
  font-weight: 500;
`;

const TestTime = styled.div`
  color: #7f8c8d;
  font-size: 0.9rem;
`;

const TestResult = styled.div`
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  background-color: ${props => 
    props.$status === 'Passed' ? '#e6f7ee' : 
    props.$status === 'Failed' ? '#fbedeb' : '#f1f5f9'};
  color: ${props => 
    props.$status === 'Passed' ? '#27ae60' : 
    props.$status === 'Failed' ? '#e74c3c' : '#7f8c8d'};
`;

function Dashboard() {
  // Simulated data - in a real app, this would come from your API
  const [stats, setStats] = useState({
    connectedDevices: 0,
    testSuites: 0,
    completedTests: 0,
    successRate: 0
  });
  
  const [recentTests, setRecentTests] = useState([]);
  
  // Simulate API call
  useEffect(() => {
    // In a real app, this would be an API call
    setTimeout(() => {
      setStats({
        connectedDevices: 3,
        testSuites: 5,
        completedTests: 28,
        successRate: 92
      });
      
      setRecentTests([
        { id: 1, name: 'Login Flow Test', time: '10 minutes ago', status: 'Passed' },
        { id: 2, name: 'Profile Update Test', time: '25 minutes ago', status: 'Failed' },
        { id: 3, name: 'Payment Processing Test', time: '1 hour ago', status: 'Passed' },
        { id: 4, name: 'Notification Test', time: '3 hours ago', status: 'Passed' },
      ]);
    }, 500);
  }, []);

  return (
    <div>
      <SectionTitle>Dashboard</SectionTitle>
      
      <DashboardContainer>
        <StatCard>
          <StatHeader>
            <StatTitle>Connected Devices</StatTitle>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17 2H7C5.89543 2 5 2.89543 5 4V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V4C19 2.89543 18.1046 2 17 2Z" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 18H12.01" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </StatHeader>
          <StatValue>{stats.connectedDevices}</StatValue>
          <ActionButton to="/devices">View Devices</ActionButton>
        </StatCard>
        
        <StatCard>
          <StatHeader>
            <StatTitle>Test Suites</StatTitle>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M14 2V8H20" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 13H8" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M16 17H8" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M10 9H9H8" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </StatHeader>
          <StatValue>{stats.testSuites}</StatValue>
          <ActionButton to="/test-suites">View Test Suites</ActionButton>
        </StatCard>
        
        <StatCard>
          <StatHeader>
            <StatTitle>Completed Tests</StatTitle>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.709 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4881 2.02168 11.3363C2.16356 9.18455 2.99721 7.13631 4.39828 5.49706C5.79935 3.85781 7.69279 2.71537 9.79619 2.24013C11.8996 1.7649 14.1003 1.98232 16.07 2.85999" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M22 4L12 14.01L9 11.01" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </StatHeader>
          <StatValue>{stats.completedTests}</StatValue>
          <ActionButton to="/results">View Results</ActionButton>
        </StatCard>
        
        <StatCard>
          <StatHeader>
            <StatTitle>Success Rate</StatTitle>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 20V10" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 20V4" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M6 20V14" stroke="#3498db" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </StatHeader>
          <StatValue>{stats.successRate}%</StatValue>
          <StatFooter $isPositive={stats.successRate >= 90}>
            {stats.successRate >= 90 ? 'Good standing' : 'Needs improvement'}
          </StatFooter>
        </StatCard>
      </DashboardContainer>
      
      <RecentTests>
        <SectionTitle>Recent Test Runs</SectionTitle>
        <TestList>
          {recentTests.map(test => (
            <TestItem key={test.id}>
              <TestName>{test.name}</TestName>
              <TestTime>{test.time}</TestTime>
              <TestResult $status={test.status}>{test.status}</TestResult>
            </TestItem>
          ))}
        </TestList>
        <ActionButton to="/results" style={{ marginTop: '20px' }}>View All Results</ActionButton>
      </RecentTests>
    </div>
  );
}

export default Dashboard; 