import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { deviceService } from '../services/deviceService';
import { sessionService } from '../services/sessionService';
import ProgressModal from './ProgressModal';

const DeviceListContainer = styled.div`
  margin-bottom: 30px;
`;

const DeviceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

const DeviceCard = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border-left: 4px solid #27ae60; /* Green border for connected devices */
`;

const DeviceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const DeviceName = styled.h3`
  margin: 0;
  font-size: 1.2rem;
`;

const DeviceStatus = styled.div`
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  background-color: ${props =>
    props.$status === 'Connected' ? '#e6f7ee' :
    props.$status === 'Available' ? '#e3f2fd' :
    props.$status === 'Online' ? '#e3f2fd' :
    props.$status === 'Offline' ? '#fbedeb' :
    props.$status === 'Busy' ? '#fff8e1' : '#f1f5f9'};
  color: ${props =>
    props.$status === 'Connected' ? '#27ae60' :
    props.$status === 'Available' ? '#1976d2' :
    props.$status === 'Online' ? '#1976d2' :
    props.$status === 'Offline' ? '#e74c3c' :
    props.$status === 'Busy' ? '#f39c12' : '#7f8c8d'};
`;

const DeviceDetails = styled.div`
  margin-top: 10px;
`;

const DetailRow = styled.div`
  display: flex;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.div`
  flex: 1;
  color: #555;
  font-size: 0.9rem;
  font-weight: 500;
`;

const DetailValue = styled.div`
  flex: 2;
  font-size: 0.9rem;
  font-weight: 500;
  background-color: #f8f9fa;
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #3498db;
`;

const ActionBar = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
`;

const ActionButton = styled.button`
  background-color: ${props => props.$variant === 'danger' ? '#e74c3c' : props.$variant === 'success' ? '#2ecc71' : '#3498db'};
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: ${props => props.$variant === 'danger' ? '#c0392b' : props.$variant === 'success' ? '#27ae60' : '#2980b9'};
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const RefreshButton = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const SectionTitle = styled.h2`
  margin-bottom: 10px;
  font-size: 1.5rem;
  color: #2c3e50;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
`;

const SectionSubTitle = styled.p`
  margin-top: 0;
  margin-bottom: 20px;
  color: #7f8c8d;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 20px;
  
  svg {
    width: 64px;
    height: 64px;
    color: #95a5a6;
  }
`;

const EmptyStateText = styled.h3`
  margin: 0 0 10px 0;
  color: #2c3e50;
`;

const EmptyStateSubtext = styled.p`
  margin: 0 0 20px 0;
  color: #7f8c8d;
`;

const StatusMessage = styled.div`
  margin-top: 10px;
  font-size: 0.9rem;
  color: ${props => props.$success ? '#27ae60' : '#e74c3c'};
  background-color: ${props => props.$success ? '#e6f7ee' : '#fbedeb'};
  padding: 8px;
  border-radius: 4px;
  text-align: center;
`;

function DeviceList() {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sessionLoading, setSessionLoading] = useState({});
  const [sessionMessage, setSessionMessage] = useState({});
  const [sessions, setSessions] = useState({});

  // Add state for progress modal
  const [progressModalVisible, setProgressModalVisible] = useState(false);
  const [progressValue, setProgressValue] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [progressError, setProgressError] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchDevices = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const fetchedDevices = await deviceService.getDevices();
      setDevices(Array.isArray(fetchedDevices) ? fetchedDevices : []);
      setLoading(false);
      setRefreshing(false);
    } catch (error) {
      console.error('Error fetching devices:', error);
      setError(`Failed to fetch devices: ${error.message}`);
      setDevices([]);
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchSessions = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/sessions');
      if (response.ok) {
        const data = await response.json();
        if (data.success && Array.isArray(data.sessions)) {
          const sessionMap = {};
          data.sessions.forEach(session => {
            if (session && session.deviceId) {
              sessionMap[session.deviceId] = session;
            }
          });
          setSessions(sessionMap);
        }
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
      // Don't set error state for session fetching as it's not critical
    }
  };

  useEffect(() => {
    fetchDevices();
    fetchSessions();

    // Poll for session updates every 5 seconds
    const sessionInterval = setInterval(fetchSessions, 5000);

    return () => clearInterval(sessionInterval);
  }, []);
  
  const handleRefresh = () => {
    fetchDevices();
    fetchSessions();
  };

  const handleTerminateSession = async (deviceId) => {
    try {
      setSessionLoading(prev => ({ ...prev, [deviceId]: true }));

      const response = await fetch('http://localhost:3001/api/sessions/terminate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deviceId }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSessionMessage(prev => ({
            ...prev,
            [deviceId]: { success: true, message: 'Session terminated successfully' }
          }));

          // Refresh sessions
          fetchSessions();

          // Clear message after 3 seconds
          setTimeout(() => {
            setSessionMessage(prev => ({ ...prev, [deviceId]: null }));
          }, 3000);
        }
      } else {
        throw new Error('Failed to terminate session');
      }
    } catch (error) {
      console.error(`Error terminating session for device ${deviceId}:`, error);
      setSessionMessage(prev => ({
        ...prev,
        [deviceId]: { success: false, message: `Error: ${error.message}` }
      }));

      setTimeout(() => {
        setSessionMessage(prev => ({ ...prev, [deviceId]: null }));
      }, 5000);
    } finally {
      setSessionLoading(prev => ({ ...prev, [deviceId]: false }));
    }
  };
  
  // Progress callback function for the session service
  const updateProgress = (progress, message, isError = false) => {
    setProgressValue(progress);
    setProgressMessage(message);
    setProgressError(isError);
  };
  
  const handleStartTestSession = async (device) => {
    try {
      const deviceId = device.udid || device.id;

      // Set loading state for this device
      setSessionLoading(prev => ({ ...prev, [deviceId]: true }));
      setSessionMessage(prev => ({ ...prev, [deviceId]: null }));

      // Show progress modal
      setProgressModalVisible(true);
      setProgressValue(0);
      setProgressMessage('Launching automation app...');
      setProgressError(false);

      console.log(`Attempting to launch automation app for device ${deviceId}`);

      // Call the new launch-app API
      const response = await fetch('http://localhost:3001/api/devices/launch-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          device: device,
          platform: device.platform
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.details || result.error || 'Failed to launch automation app');
      }

      setProgressValue(80);
      setProgressMessage('Opening automation app...');

      // Open the automation app in a new tab
      if (result.url) {
        window.open(result.url, '_blank');
      }

      setProgressValue(100);
      setProgressMessage('Automation app launched successfully!');

      // Hide progress modal after 1 second
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 1000);

      // Set success message
      setSessionMessage(prev => ({
        ...prev,
        [deviceId]: { success: true, message: result.alreadyRunning ? 'Automation app was already running' : 'Automation app launched successfully' }
      }));

      // Refresh sessions to get updated state
      fetchSessions();

      // Clear message after 5 seconds
      setTimeout(() => {
        setSessionMessage(prev => ({ ...prev, [deviceId]: null }));
      }, 5000);

    } catch (error) {
      console.error(`Error launching automation app for device ${device.udid || device.id}:`, error);

      setProgressValue(100);
      setProgressMessage('Failed to launch automation app');
      setProgressError(true);

      // Hide progress modal after showing error for 3 seconds
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 3000);

      // Set error message
      setSessionMessage(prev => ({
        ...prev,
        [device.udid || device.id]: { success: false, message: `Error: ${error.message}` }
      }));

      // Keep error message longer - 10 seconds
      setTimeout(() => {
        setSessionMessage(prev => ({ ...prev, [device.udid || device.id]: null }));
      }, 10000);

    } finally {
      // Clear loading state
      setSessionLoading(prev => ({ ...prev, [device.udid || device.id]: false }));
    }
  };
  

  
  return (
    <div>
      {progressModalVisible && (
        <ProgressModal 
          progress={progressValue}
          message={progressMessage}
          isError={progressError}
        />
      )}
      <DeviceListContainer>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <SectionTitle>Local Devices</SectionTitle>
            <div style={{ fontSize: '14px', color: '#555', marginTop: '-15px', marginBottom: '15px' }}>
              Showing real device information
            </div>
          </div>
          <RefreshButton onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? 'Refreshing...' : 'Refresh Devices'}
          </RefreshButton>
        </div>

        {error ? (
          <EmptyState>
            <EmptyStateIcon>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </EmptyStateIcon>
            <EmptyStateText>Error Loading Devices</EmptyStateText>
            <EmptyStateSubtext>{error}</EmptyStateSubtext>
            <RefreshButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? 'Retrying...' : 'Retry'}
            </RefreshButton>
          </EmptyState>
        ) : loading ? (
          <p>Loading devices...</p>
        ) : devices.length > 0 ? (
          <DeviceGrid>
            {devices.filter(device => device && (device.udid || device.id)).map((device) => {
              const deviceId = device.udid || device.id;
              const hasActiveSession = sessions[deviceId];
              const sessionStatus = hasActiveSession ? 'Connected' : (device.status === 'Online' ? 'Available' : device.status || device.state || 'Unknown');

              return (
                <DeviceCard key={deviceId}>
                  <DeviceHeader>
                    <DeviceName>{device.name}</DeviceName>
                    <DeviceStatus $status={sessionStatus}>{sessionStatus}</DeviceStatus>
                  </DeviceHeader>
                  <DeviceDetails>
                    <DetailRow>
                      <DetailLabel>UDID</DetailLabel>
                      <DetailValue>{deviceId}</DetailValue>
                    </DetailRow>
                    <DetailRow>
                      <DetailLabel>Platform</DetailLabel>
                      <DetailValue>{device.platform}</DetailValue>
                    </DetailRow>
                    <DetailRow>
                      <DetailLabel>OS Version</DetailLabel>
                      <DetailValue>{device.osVersion || device.version || 'N/A'}</DetailValue>
                    </DetailRow>
                    <DetailRow>
                      <DetailLabel>Model</DetailLabel>
                      <DetailValue>{device.model || 'N/A'}</DetailValue>
                    </DetailRow>
                    {hasActiveSession && (
                      <>
                        <DetailRow>
                          <DetailLabel>Session Port</DetailLabel>
                          <DetailValue>{hasActiveSession.port}</DetailValue>
                        </DetailRow>
                        <DetailRow>
                          <DetailLabel>Session Started</DetailLabel>
                          <DetailValue>{new Date(hasActiveSession.startTime).toLocaleTimeString()}</DetailValue>
                        </DetailRow>
                      </>
                    )}
                  </DeviceDetails>
                  {sessionMessage[deviceId] && (
                    <StatusMessage $success={sessionMessage[deviceId].success}>
                      {sessionMessage[deviceId].message}
                    </StatusMessage>
                  )}
                  <ActionBar>
                    {hasActiveSession ? (
                      <>
                        <ActionButton
                          onClick={() => window.open(`http://localhost:${hasActiveSession.port}`, '_blank')}
                          disabled={sessionLoading[deviceId]}
                        >
                          Open App
                        </ActionButton>
                        <ActionButton
                          $variant="danger"
                          onClick={() => handleTerminateSession(deviceId)}
                          disabled={sessionLoading[deviceId]}
                        >
                          {sessionLoading[deviceId] ? 'Terminating...' : 'Disconnect Session'}
                        </ActionButton>
                      </>
                    ) : (
                      <ActionButton
                        $variant="success"
                        onClick={() => handleStartTestSession(device)}
                        disabled={sessionLoading[deviceId]}
                      >
                        {sessionLoading[deviceId] ? 'Starting...' : 'Open Automation App'}
                      </ActionButton>
                    )}
                  </ActionBar>
                </DeviceCard>
              );
            })}
          </DeviceGrid>
        ) : (
          <EmptyState>
            <EmptyStateIcon>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </EmptyStateIcon>
            <EmptyStateText>No physical devices found</EmptyStateText>
            <EmptyStateSubtext>Connect devices via USB to see them here.</EmptyStateSubtext>
            <RefreshButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? 'Refreshing...' : 'Refresh Device List'}
            </RefreshButton>
          </EmptyState>
        )}
      </DeviceListContainer>
    </div>
  );
}

export default DeviceList; 