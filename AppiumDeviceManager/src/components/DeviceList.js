import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  Button, 
  Box, 
  CircularProgress 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import { deviceService } from '../services/deviceService';
import { sessionService } from '../services/sessionService';
import ProgressModal from './ProgressModal';
import browserstackService from '../services/browserstackService';

const DeviceListContainer = styled.div`
  margin-bottom: 30px;
`;

const DeviceGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

const DeviceCard = styled.div`
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  border-left: 4px solid #27ae60; /* Green border for connected devices */
`;

const DeviceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const DeviceName = styled.h3`
  margin: 0;
  font-size: 1.2rem;
`;

const DeviceStatus = styled.div`
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  background-color: ${props => 
    props.$status === 'Online' ? '#e6f7ee' : 
    props.$status === 'Offline' ? '#fbedeb' : 
    props.$status === 'Busy' ? '#fff8e1' : '#f1f5f9'};
  color: ${props => 
    props.$status === 'Online' ? '#27ae60' : 
    props.$status === 'Offline' ? '#e74c3c' : 
    props.$status === 'Busy' ? '#f39c12' : '#7f8c8d'};
`;

const DeviceDetails = styled.div`
  margin-top: 10px;
`;

const DetailRow = styled.div`
  display: flex;
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailLabel = styled.div`
  flex: 1;
  color: #555;
  font-size: 0.9rem;
  font-weight: 500;
`;

const DetailValue = styled.div`
  flex: 2;
  font-size: 0.9rem;
  font-weight: 500;
  background-color: #f8f9fa;
  padding: 6px 10px;
  border-radius: 4px;
  border-left: 3px solid #3498db;
`;

const ActionBar = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
`;

const ActionButton = styled.button`
  background-color: ${props => props.$variant === 'danger' ? '#e74c3c' : props.$variant === 'success' ? '#2ecc71' : '#3498db'};
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: ${props => props.$variant === 'danger' ? '#c0392b' : props.$variant === 'success' ? '#27ae60' : '#2980b9'};
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const RefreshButton = styled.button`
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const SectionTitle = styled.h2`
  margin-bottom: 10px;
  font-size: 1.5rem;
  color: #2c3e50;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
`;

const SectionSubTitle = styled.p`
  margin-top: 0;
  margin-bottom: 20px;
  color: #7f8c8d;
`;

const CloudRunContainer = styled.div`
  margin-top: 40px;
`;

const TabsContainer = styled.div`
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 20px;
`;

const TabButton = styled.button`
  padding: 12px 24px;
  cursor: pointer;
  border: none;
  background-color: transparent;
  border-bottom: 3px solid transparent;
  font-size: 1.1rem;
  color: #6c757d;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: #343a40;
  }

  ${({ $active }) =>
    $active &&
    `
    border-bottom: 3px solid #3498db;
    font-weight: 600;
    color: #3498db;
  `}
`;

const TabContent = styled.div`
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #ccc;
  box-sizing: border-box;
`;

const ConnectButton = styled(ActionButton)`
  width: auto;
  margin-bottom: 15px;
`;

const Dropdown = styled.select`
  width: 100%;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
  border: 1px solid #ccc;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const EmptyStateIcon = styled.div`
  margin-bottom: 20px;
  
  svg {
    width: 64px;
    height: 64px;
    color: #95a5a6;
  }
`;

const EmptyStateText = styled.h3`
  margin: 0 0 10px 0;
  color: #2c3e50;
`;

const EmptyStateSubtext = styled.p`
  margin: 0 0 20px 0;
  color: #7f8c8d;
`;

const StatusMessage = styled.div`
  margin-top: 10px;
  font-size: 0.9rem;
  color: ${props => props.$success ? '#27ae60' : '#e74c3c'};
  background-color: ${props => props.$success ? '#e6f7ee' : '#fbedeb'};
  padding: 8px;
  border-radius: 4px;
  text-align: center;
`;

function DeviceList() {
  const [devices, setDevices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sessionLoading, setSessionLoading] = useState({});
  const [sessionMessage, setSessionMessage] = useState({});
  const [activeTab, setActiveTab] = useState('browserstack');
  
  // BrowserStack state
  const [bsUsername, setBsUsername] = useState('');
  const [bsAccessKey, setBsAccessKey] = useState('');
  const [bsDevices, setBsDevices] = useState([]);
  const [bsConnected, setBsConnected] = useState(false);
  const [bsConnecting, setBsConnecting] = useState(false);
  const [bsError, setBsError] = useState('');
  const [selectedBsDevice, setSelectedBsDevice] = useState('');
  const [runTestLoading, setRunTestLoading] = useState(false);
  const [runTestMessage, setRunTestMessage] = useState(null);
  const [testScripts, setTestScripts] = useState([]);
  const [selectedTest, setSelectedTest] = useState('');
  
  // Add state for progress modal
  const [progressModalVisible, setProgressModalVisible] = useState(false);
  const [progressValue, setProgressValue] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [progressError, setProgressError] = useState(false);
  
  const fetchDevices = async () => {
    try {
      setRefreshing(true);
      // In a real app, this would call your API
      const fetchedDevices = await deviceService.getDevices();
      setDevices(fetchedDevices);
      setLoading(false);
      setRefreshing(false);
    } catch (error) {
      console.error('Error fetching devices:', error);
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    fetchDevices();

    const fetchTestScripts = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/tests');
        if (!response.ok) {
          throw new Error('Could not fetch test scripts');
        }
        const scripts = await response.json();
        setTestScripts(scripts);
      } catch (error) {
        console.error('Error fetching test scripts:', error);
      }
    };

    fetchTestScripts();
  }, []);
  
  const handleRefresh = () => {
    fetchDevices();
  };
  
  const handleRestart = (deviceId) => {
    // In a real app, this would call your API to restart the device
    console.log(`Restarting device ${deviceId}`);
  };
  
  const handleClose = (deviceId) => {
    // In a real app, this would call your API to close the session
    console.log(`Closing session for device ${deviceId}`);
  };
  
  // Progress callback function for the session service
  const updateProgress = (progress, message, isError = false) => {
    setProgressValue(progress);
    setProgressMessage(message);
    setProgressError(isError);
  };
  
  const handleStartTestSession = async (device) => {
    try {
      // Set loading state for this device
      setSessionLoading(prev => ({ ...prev, [device.id]: true }));
      setSessionMessage(prev => ({ ...prev, [device.id]: null }));
      
      // Show progress modal
      setProgressModalVisible(true);
      setProgressValue(0);
      setProgressMessage('Preparing to start main app...');
      setProgressError(false);
      
      // First check if main app is running
      console.log(`Attempting to open main app for device ${device.id}`);
      
      // Start a test session for the device with progress updates
      const result = await sessionService.startTestSession(device, updateProgress);
      console.log(`Main app opened with URL: ${result.sessionUrl}`);
      
      // Hide progress modal after 1 second
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 1000);
      
      // Set success message
      setSessionMessage(prev => ({ 
        ...prev, 
        [device.id]: { success: true, message: `Main app opened successfully` }
      }));
      
      // Clear message after 5 seconds
      setTimeout(() => {
        setSessionMessage(prev => ({ ...prev, [device.id]: null }));
      }, 5000);
      
    } catch (error) {
      console.error(`Error opening main app for device ${device.id}:`, error);
      
      // Hide progress modal after showing error for 3 seconds
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 3000);
      
      // Create a user-friendly error message
      let errorMessage = error.message || 'Could not open main app';
      
      // Check for common connection issues
      if (errorMessage.includes('not reachable')) {
        errorMessage = 'The automation app is not running. Please start it with "python run.py" first.';
      } else if (errorMessage.includes('popup blocker')) {
        errorMessage = 'Popup blocker prevented opening the main app. Please allow popups for this site.';
      }
      
      // Set error message
      setSessionMessage(prev => ({ 
        ...prev, 
        [device.id]: { success: false, message: `Error: ${errorMessage}` }
      }));
      
      // Keep error message longer - 10 seconds
      setTimeout(() => {
        setSessionMessage(prev => ({ ...prev, [device.id]: null }));
      }, 10000);
      
    } finally {
      // Clear loading state
      setSessionLoading(prev => ({ ...prev, [device.id]: false }));
    }
  };
  
  const handleBsConnect = async () => {
    setBsConnecting(true);
    setBsError('');
    try {
      console.log('Fetching devices...');
      const devices = await browserstackService.getDevices(bsUsername, bsAccessKey);
      console.log('Devices received:', devices);
      if (devices && devices.length > 0) {
        console.log('First device structure:', JSON.stringify(devices[0], null, 2));
      }
      setBsDevices(devices);
      setBsConnected(true);
      console.log('bsDevices state updated, bsConnected set to true');
    } catch (error) {
      setBsError(error.toString());
      setBsConnected(false);
    } finally {
      setBsConnecting(false);
    }
  };

  const handleBsDisconnect = () => {
    setBsConnected(false);
    setBsDevices([]);
    setBsUsername('');
    setBsAccessKey('');
    setBsError('');
  };

  const [testSessionUrl, setTestSessionUrl] = useState('');

  const handleRunTest = async () => {
    console.log('handleRunTest called with:', { selectedBsDevice, selectedTest });
    
    if (!selectedBsDevice) {
      setRunTestMessage({ success: false, text: 'Please select a device.' });
      return;
    }

    let device;
    try {
      device = JSON.parse(selectedBsDevice);
      console.log('Parsed device:', device);
    } catch (e) {
      console.error('Error parsing device:', e);
      setRunTestMessage({ 
        success: false, 
        text: 'Invalid device selection',
        details: 'The selected device information is invalid. Please try selecting the device again.'
      });
      return;
    }

    // Extract device properties from the device object
    const deviceName = device.device || device.name || '';
    const osVersion = device.os_version || device.osVersion || '';
    const osName = (device.os || '').toLowerCase();
    
    // Basic validation
    if (!deviceName || !osVersion || !osName) {
      console.error('Incomplete device information:', { device });
      setRunTestMessage({
        success: false,
        text: 'Incomplete device information. Please select a valid device.'
      });
      return;
    }

    setRunTestLoading(true);
    setRunTestMessage(null);
    setTestSessionUrl('');

    // Show progress modal
    setProgressModalVisible(true);
    setProgressValue(0);
    setProgressMessage('Initializing test session...');
    setProgressError(false);

    try {
      setProgressValue(20);
      setProgressMessage('Connecting to BrowserStack...');
      
      // First, verify BrowserStack connection
      if (!bsConnected) {
        await handleBsConnect();
        if (!bsConnected) {
          throw new Error('Failed to connect to BrowserStack. Please check your credentials.');
        }
      }

      setProgressValue(40);
      setProgressMessage('Starting test session...');
      
      // Prepare test data
      const testData = {
        device: deviceName,
        os_version: osVersion,
        os: osName,
        testScript: selectedTest || 'mobile_test.py' // Default to our mobile test script
      };

      setProgressValue(60);
      setProgressMessage('Executing test...');
      
      // Make the API call to run the test
      const response = await fetch('http://localhost:3001/api/run-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-BS-User': bsUsername,
          'X-BS-Key': bsAccessKey,
        },
        body: JSON.stringify(testData),
      });

      // Get the response as text first
      const responseText = await response.text();
      let result;
      
      // Try to parse as JSON
      try {
        result = responseText ? JSON.parse(responseText) : {};
      } catch (e) {
        console.error('Failed to parse response as JSON:', { status: response.status, text: responseText });
        throw new Error(`Invalid response from server: ${response.status} ${response.statusText}. Response: ${responseText.substring(0, 200)}...`);
      }
      
      console.log('Test session response:', { 
        status: response.status, 
        statusText: response.statusText,
        body: result 
      });
      
      if (!response.ok) {
        const errorMessage = result.error || `Request failed with status ${response.status}`;
        const details = result.details || result.message || response.statusText;
        const error = new Error(errorMessage);
        error.response = response;
        error.details = details;
        throw error;
      }

      // Handle successful response
      setProgressValue(90);
      setProgressMessage('Test completed successfully!');
      
      // Extract session URL if available
      const sessionUrl = result.sessionUrl || 
                       (result.output && result.output.match(/https?:\/\/[^\s]+/)?.[0]);
      
      if (sessionUrl) {
        setTestSessionUrl(sessionUrl);
        setRunTestMessage({
          success: true,
          text: 'Test executed successfully!',
          details: 'Click the link below to view the test session.',
          url: sessionUrl
        });
      } else {
        setRunTestMessage({
          success: true,
          text: 'Test executed successfully!',
          details: result.output || 'No additional details available.'
        });
      }
      
      // Hide progress modal after a delay
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 2000);
      
    } catch (error) {
      console.error('Test execution failed:', error);
      
      // Update progress modal with error
      setProgressValue(100);
      setProgressMessage('Test execution failed');
      setProgressError(true);
      
      // Set error message
      const errorDetails = error.details || error.message || 'Unknown error occurred';
      setRunTestMessage({
        success: false,
        text: 'Test execution failed',
        details: errorDetails,
        error: true
      });
      
      // Hide progress modal after a delay
      setTimeout(() => {
        setProgressModalVisible(false);
      }, 3000);
    } finally {
      setRunTestLoading(false);
    }
  };
  
  return (
    <div>
      {progressModalVisible && (
        <ProgressModal 
          progress={progressValue}
          message={progressMessage}
          isError={progressError}
        />
      )}
      <DeviceListContainer>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <div>
            <SectionTitle>Local Devices</SectionTitle>
            <div style={{ fontSize: '14px', color: '#555', marginTop: '-15px', marginBottom: '15px' }}>
              Showing real device information
            </div>
          </div>
          <RefreshButton onClick={handleRefresh} disabled={refreshing}>
            {refreshing ? 'Refreshing...' : 'Refresh Devices'}
          </RefreshButton>
        </div>

        {loading ? (
          <p>Loading devices...</p>
        ) : devices.length > 0 ? (
          <DeviceGrid>
            {devices.map((device) => (
              <DeviceCard key={device.udid}>
                <DeviceHeader>
                  <DeviceName>{device.name}</DeviceName>
                  <DeviceStatus $status={device.state}>{device.state}</DeviceStatus>
                </DeviceHeader>
                <DeviceDetails>
                  <DetailRow>
                    <DetailLabel>UDID</DetailLabel>
                    <DetailValue>{device.udid}</DetailValue>
                  </DetailRow>
                  <DetailRow>
                    <DetailLabel>Platform</DetailLabel>
                    <DetailValue>{device.platform}</DetailValue>
                  </DetailRow>
                  <DetailRow>
                    <DetailLabel>OS Version</DetailLabel>
                    <DetailValue>{device.osVersion}</DetailValue>
                  </DetailRow>
                  <DetailRow>
                    <DetailLabel>Model</DetailLabel>
                    <DetailValue>{device.model || 'N/A'}</DetailValue>
                  </DetailRow>
                </DeviceDetails>
                {sessionMessage[device.udid] && (
                  <StatusMessage $success={!sessionMessage[device.udid].includes('Error')}>
                    {sessionMessage[device.udid]}
                  </StatusMessage>
                )}
                <ActionBar>
                  <ActionButton 
                    onClick={() => handleRestart(device.udid)}
                    disabled={sessionLoading[device.udid]}
                  >
                    Restart
                  </ActionButton>
                  <ActionButton 
                    $variant="danger" 
                    onClick={() => handleClose(device.udid)}
                    disabled={sessionLoading[device.udid]}
                  >
                    Close
                  </ActionButton>
                  <ActionButton
                    $variant="success"
                    onClick={() => handleStartTestSession(device)}
                    disabled={sessionLoading[device.udid]}
                  >
                    {sessionLoading[device.udid] ? 'Starting...' : 'Open Automation App'}
                  </ActionButton>
                </ActionBar>
              </DeviceCard>
            ))}
          </DeviceGrid>
        ) : (
          <EmptyState>
            <EmptyStateIcon>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </EmptyStateIcon>
            <EmptyStateText>No physical devices found</EmptyStateText>
            <EmptyStateSubtext>Connect devices via USB to see them here.</EmptyStateSubtext>
            <RefreshButton onClick={handleRefresh} disabled={refreshing}>
              {refreshing ? 'Refreshing...' : 'Refresh Device List'}
            </RefreshButton>
          </EmptyState>
        )}
      </DeviceListContainer>
      
      <CloudRunContainer>
        <SectionTitle>Cloud Run</SectionTitle>
        <TabsContainer>
          <TabButton 
            $active={activeTab === 'browserstack'} 
            onClick={() => setActiveTab('browserstack')}
          >
            BrowserStack
          </TabButton>
          <TabButton 
            $active={activeTab === 'others'}
            onClick={() => setActiveTab('others')}
          >
            Others
          </TabButton>
        </TabsContainer>

        {activeTab === 'browserstack' && (
          <TabContent>
            <div className="input-field">
              <label htmlFor="bs-username">User Name</label>
              <Input 
                id="bs-username"
                type="text" 
                placeholder="Your BrowserStack Username" 
                value={bsUsername}
                onChange={(e) => setBsUsername(e.target.value)}
                disabled={bsConnecting || bsConnected}
              />
            </div>
            <div className="input-field">
              <label htmlFor="bs-access-key">Access Key</label>
              <Input 
                id="bs-access-key"
                type="password" 
                placeholder="Your BrowserStack Access Key" 
                value={bsAccessKey}
                onChange={(e) => setBsAccessKey(e.target.value)}
                disabled={bsConnecting || bsConnected}
              />
            </div>
            
            {bsConnected ? (
              <ConnectButton onClick={handleBsDisconnect}>Clear</ConnectButton>
            ) : (
              <ConnectButton onClick={handleBsConnect} disabled={bsConnecting || !bsUsername || !bsAccessKey}>
                {bsConnecting ? 'Getting Devices...' : 'Get Devices'}
              </ConnectButton>
            )}

            {bsError && <div className="status-message error">{bsError}</div>}
            
            {bsConnected && bsDevices.length > 0 && (
              <div style={{ marginTop: '20px' }}>
                <div className="input-field">
                  <label htmlFor="bs-device">Select Device</label>
                  <Dropdown
                    id="bs-device"
                    value={selectedBsDevice}
                    onChange={(e) => setSelectedBsDevice(e.target.value)}
                    disabled={runTestLoading}
                  >
                    <option value="">Select a device</option>
                    {console.log('Rendering bsDevices:', bsDevices)}
                    {Array.isArray(bsDevices) && bsDevices.length > 0 ? (
                      bsDevices.map((device, index) => (
                        <option 
                          key={`device-${device.id || index}`} 
                          value={JSON.stringify(device)}
                        >
                          {device.name}
                        </option>
                      ))
                    ) : (
                      <option disabled>No devices available</option>
                    )}
                  </Dropdown>
                </div>

                <div className="input-field" style={{ marginTop: '15px' }}>
                  <label htmlFor="bs-test">Select Test</label>
                  <Dropdown
                    id="bs-test"
                    value={selectedTest}
                    onChange={(e) => setSelectedTest(e.target.value)}
                    disabled={runTestLoading || testScripts.length === 0}
                  >
                    <option value="">Select a test script</option>
                    {testScripts.map((script) => (
                      <option key={script} value={script}>
                        {script}
                      </option>
                    ))}
                  </Dropdown>
                </div>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
                  <Button 
                    onClick={handleRunTest} 
                    disabled={runTestLoading}
                    variant="contained" 
                    color="primary"
                    startIcon={runTestLoading ? <CircularProgress size={20} /> : <PlayArrowIcon />}
                    fullWidth
                  >
                    {runTestLoading ? 'Starting Test Session...' : 'Run Test on BrowserStack'}
                  </Button>
                  
                  {testSessionUrl && (
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => window.open(testSessionUrl, '_blank')}
                      startIcon={<OpenInNewIcon />}
                      fullWidth
                    >
                      View Test Session
                    </Button>
                  )}
                </Box>

                {runTestMessage && (
                  <StatusMessage $success={runTestMessage.success} style={{ marginTop: '15px' }}>
                    {runTestMessage.text}
                  </StatusMessage>
                )}
              </div>
            )}
          </TabContent>
        )}
        {activeTab === 'others' && (
          <TabContent>
            <p>This section is under construction.</p>
          </TabContent>
        )}
      </CloudRunContainer>
    </div>
  );
}

export default DeviceList; 