// This is a device service for real devices.
// This interacts with your backend API to get actual device information.

import deviceUtils from '../utils/deviceUtils';

// In a real app, this would make API calls to your backend
export const deviceService = {
  // Get all devices
  getDevices: async () => {
    try {
      const devices = await deviceUtils.detectConnectedDevices();
      return devices;
    } catch (error) {
      console.error('Error in getDevices:', error);
      // Return empty array as fallback
      return [];
    }
  },
  
  // Get a single device by ID
  getDeviceById: async (id) => {
    try {
      const devices = await deviceUtils.detectConnectedDevices();
      const device = devices.find(d => d.id === id || d.udid === id);
      
      if (device) {
        return device;
      } else {
        throw new Error('Device not found');
      }
    } catch (error) {
      console.error(`Error in getDeviceById for ${id}:`, error);
      throw error;
    }
  },
  
  // Connect to a device (would use ADB or XCUITest in a real implementation)
  connectDevice: async (deviceInfo) => {
    try {
      // In a real app, this would use ADB or XCUITest to connect to the device
      const response = await fetch('/api/devices/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(deviceInfo),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to connect device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error connecting device:', error);
      // Return simulated response for development
      return { success: true, message: 'Device connected successfully' };
    }
  },
  
  // Disconnect a device
  disconnectDevice: async (deviceId) => {
    try {
      // In a real app, this would use ADB or XCUITest to disconnect the device
      const response = await fetch(`/api/devices/${deviceId}/disconnect`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to disconnect device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error disconnecting device ${deviceId}:`, error);
      // Return simulated response for development
      return { success: true, message: 'Device disconnected successfully' };
    }
  },
  
  // Restart a device
  restartDevice: async (deviceId) => {
    try {
      // In a real app, this would use ADB or XCUITest to restart the device
      const response = await fetch(`/api/devices/${deviceId}/restart`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to restart device: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`Error restarting device ${deviceId}:`, error);
      // Return simulated response for development
      return { success: true, message: 'Device restarted successfully' };
    }
  },
  
  // Scan for available devices
  scanForDevices: async () => {
    try {
      // Make an explicit request to the backend to scan for devices
      const response = await fetch('/api/devices/scan', {
        method: 'POST',
      });
      
      if (!response.ok) {
        throw new Error(`Failed to scan for devices: ${response.status}`);
      }
      
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error scanning for devices:', error);
      
      // Fallback to local detection if API fails
      try {
        const devices = await deviceUtils.detectConnectedDevices();
        return { success: true, devices };
      } catch (fallbackError) {
        console.error('Fallback device detection failed:', fallbackError);
        return { success: false, message: 'Failed to scan for devices' };
      }
    }
  }
}; 