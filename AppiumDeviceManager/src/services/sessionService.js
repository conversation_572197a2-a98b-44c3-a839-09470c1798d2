import portUtils from '../utils/portUtils';
import urlHelper from '../utils/urlHelper';
import serverService from './serverService';

// Function to check if the main app is reachable
const checkMainAppAvailability = async () => {
  // Mock implementation to avoid errors
  console.log('Using mock implementation of checkMainAppAvailability');
  return true; // Always return true in mock mode
};

// This service will handle opening the main app with device ID
export const sessionService = {
  // Start a test session for a device - this automatically starts the main app if needed
  startTestSession: async (device, progressCallback = null) => {
    try {
      console.log('Starting mock session with device:', device);
      
      // Update progress
      if (progressCallback) progressCallback(20, 'Simulating app start...');
      
      // Short delay to simulate startup
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      if (progressCallback) progressCallback(60, 'Preparing session...');
      
      // Create URL with the device ID and platform using our helper
      const sessionUrl = urlHelper.formatMainAppUrl(device.udid || '', device.platform || '');
      
      // Actually try to open the window
      console.log('Opening main app URL:', sessionUrl);
      const newWindow = window.open(sessionUrl, '_blank');
      
      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        throw new Error('Could not open the main app. Please check your popup blocker settings.');
      }
      
      // Short delay to simulate further processing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (progressCallback) progressCallback(100, 'Session ready!');
      
      console.log('Session URL:', sessionUrl);
      
      return { 
        success: true, 
        message: 'Main app opened with device information',
        device,
        sessionUrl
      };
    } catch (error) {
      console.error('Error in session service:', error);
      if (progressCallback) progressCallback(100, `Error: ${error.message}`, true);
      throw new Error(`Failed to start session: ${error.message || 'Unknown error'}`);
    }
  },
  
  // Close a test session
  closeSession: async (deviceId) => {
    try {
      // Mock implementation
      return { success: true, message: 'Session closed' };
    } catch (error) {
      console.error(`Error closing session for device ${deviceId}:`, error);
      throw new Error(`Failed to close session: ${error.message || 'Unknown error'}`);
    }
  }
};

export default sessionService; 