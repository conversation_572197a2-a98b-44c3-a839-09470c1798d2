const express = require('express');
const cors = require('cors');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

const app = express();
app.use(cors());
app.use(express.json());

// Explicit CORS preflight handler for custom headers
app.options('/api/devices', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, X-BS-User, X-BS-Key');
  res.sendStatus(204);
});

// API endpoint for BrowserStack devices
app.get('/api/devices', async (req, res) => {
  const { 'x-bs-user': username, 'x-bs-key': accessKey } = req.headers;
  console.log('[API] Received GET request for /api/devices');

  if (!username || !accessKey) {
    console.error('[API] Missing username or accessKey in headers.');
    return res.status(400).json({ error: 'Username and Access Key are required.' });
  }

  const credentials = Buffer.from(`${username}:${accessKey}`).toString('base64');
  const url = 'https://api-cloud.browserstack.com/app-automate/devices.json';

  try {
    console.log(`[API] Forwarding request to BrowserStack: ${url}`);
    const bsResponse = await fetch(url, {
      method: 'GET',
      headers: { 'Authorization': `Basic ${credentials}` },
    });

    const responseBodyText = await bsResponse.text();
    console.log(`[API] BrowserStack response status: ${bsResponse.status}`);

    if (!bsResponse.ok) {
      console.error(`[API] BrowserStack request failed: ${responseBodyText}`);
      return res.status(bsResponse.status).json({ error: `BrowserStack API Error: ${responseBodyText}` });
    }

    res.setHeader('Content-Type', 'application/json');
    res.send(responseBodyText);

  } catch (error) {
    console.error('[API] Internal server error:', error);
    res.status(500).json({ error: `Internal Server Error: ${error.message}` });
  }
});

// Endpoint to get the list of test scripts
app.get(['/api/tests', '/api/tests/'], (req, res) => {
  const testsDirectory = path.join(__dirname, 'tests');
  fs.readdir(testsDirectory, (err, files) => {
    if (err) {
      console.error('Could not list the directory.', err);
      return res.status(500).send('Unable to read test scripts.');
    }
    const testFiles = files.filter(file => file.endsWith('.py'));
    res.json(testFiles);
  });
});

// Endpoint to run a test using BrowserStack SDK
app.post('/api/run-test', async (req, res) => {
  const { device, os_version, os, testScript } = req.body;
  const { 'x-bs-user': username, 'x-bs-key': accessKey } = req.headers;

  if (!device || !os_version || !os) {
    return res.status(400).json({
      error: 'Missing required parameters',
      details: 'Device, OS version, and OS are required'
    });
  }

  if (!username || !accessKey) {
    return res.status(401).json({
      error: 'Unauthorized',
      details: 'BrowserStack credentials are required'
    });
  }

  let testFile;
  let pythonProcess;
  let timeout;

  const cleanup = () => {
    // Clean up the timeout
    if (timeout) clearTimeout(timeout);
    
    // Commented out test file deletion to preserve the file after test run
    /*
    if (testFile && fs.existsSync(testFile)) {
      try {
        fs.unlinkSync(testFile);
        console.log('Test file cleaned up:', testFile);
      } catch (e) {
        console.error('Error cleaning up test file:', e);
      }
    }
    */
  };

  try {
    // Create a test name based on the device and timestamp
    const testName = `Test on ${device} (${os} ${os_version}) - ${new Date().toISOString()}`;
    const buildName = `Build ${new Date().toISOString().split('T')[0]}`;
    
    // Use the selected test script
    const testDir = path.join(__dirname, 'tests');
    testFile = path.join(testDir, testScript || 'mobile_test.py');
    
    // Ensure test file exists
    if (!fs.existsSync(testFile)) {
      return res.status(404).json({
        error: 'Test script not found',
        details: `The test script ${testScript} does not exist`
      });
    }
    
    // Ensure tests directory exists
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // Set environment variables for the test
    const env = {
      ...process.env,
      BROWSERSTACK_USERNAME: username,
      BROWSERSTACK_ACCESS_KEY: accessKey,
      BROWSERSTACK_DEVICE: device,
      BROWSERSTACK_OS: os,
      BROWSERSTACK_OS_VERSION: os_version
    };
    
    // Prepare command arguments
    const args = [testFile, device, os_version, os];
    
    // Log the command being executed
    console.log('Executing test with arguments:', { args, env: { ...env, BROWSERSTACK_ACCESS_KEY: '***' } });
    
    // Execute the test script with arguments
    pythonProcess = spawn('python3', args, {
      env,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    console.log(`Test process started with PID: ${pythonProcess.pid}`);
    
    let stdout = '';
    let stderr = '';
    
    // Set up stdout/stderr handlers
    pythonProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(`[TEST OUTPUT] ${output}`);
      stdout += output;
    });
    
    pythonProcess.stderr.on('data', (data) => {
      const error = data.toString();
      console.error(`[TEST ERROR] ${error}`);
      stderr += error;
    });
    
    // Handle process exit
    const handleExit = (code, signal) => {
      console.log(`Test process exited with code ${code}, signal: ${signal}`);
      cleanup();
      
      if (code !== 0) {
        console.error(`Test execution failed with code ${code}: ${stderr}`);
        if (!res.headersSent) {
          return res.status(500).json({
            error: 'Test execution failed',
            details: stderr || 'Unknown error occurred',
            stdout: stdout,
            stderr: stderr,
            exitCode: code
          });
        }
      } else if (!res.headersSent) {
        // Return success response
        res.status(200).json({
          message: 'Test executed successfully',
          output: stdout,
          error: stderr || null,
          exitCode: code
        });
      }
    };
    
    pythonProcess.on('close', handleExit);
    
    // Handle process errors
    pythonProcess.on('error', (error) => {
      console.error('Failed to start test process:', error);
      cleanup();
      
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Failed to start test process',
          details: error.message
        });
      }
    });
    
    // Set a timeout for the test execution
    timeout = setTimeout(() => {
      if (pythonProcess && !pythonProcess.killed) {
        console.error('Test execution timed out - terminating process');
        pythonProcess.kill('SIGTERM');
        
        if (!res.headersSent) {
          res.status(500).json({
            error: 'Test execution timed out',
            details: 'The test took too long to complete (5 minute timeout)'
          });
        }
      }
    }, 300000); // 5 minutes timeout
    
  } catch (error) {
    console.error('Error in test execution:', error);
    cleanup();
    
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to execute test',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
});

// Serve static files from the 'dist' directory
app.use(express.static(path.join(__dirname, 'dist')));

// For any other request, serve the index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`Backend server running on http://localhost:${PORT}`);
}); 