{"name": "WishList NZ", "created": "2025-06-28 22:27:01", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "3375ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "4853ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1160ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "1859ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "w8dueydByT", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "test_case_steps_count": 6, "timestamp": 1750035260376, "type": "multiStep"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "4108ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "executionTime": "2564ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "1765ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "2353ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 20, "timestamp": 1746839941281, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "2208ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "2177ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "WbxRVpWtjw", "executionTime": "2470ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "zzd5ufNDih", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750034913531, "type": "tap", "executionTime": "2432ms"}, {"action_id": "oWLIFhrzr1", "executionTime": "2204ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1746837687234, "type": "tap"}, {"action_id": "BzTvnSrykE", "executionTime": "1695ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746837736927, "type": "waitTill"}, {"action_id": "alaozIePOy", "executionTime": "2107ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1746837800749, "type": "tap"}, {"action_id": "Z86YU6Mq0g", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750034929452, "type": "tap", "executionTime": "2061ms"}, {"type": "waitTill", "timestamp": 1751113593305, "x": 0, "y": 0, "method": "coordinates", "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "timeout": 10, "interval": 0.5, "action_id": "cqYJCDIq3R"}, {"action_id": "PH8FFnzohm", "executionTime": "2222ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1746838255053, "type": "tap"}, {"action_id": "alaozIePOy", "executionTime": "3591ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1746839229725, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "3187ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "executionTime": "1345ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 20, "timestamp": 1746100313636, "type": "waitTill"}, {"action_id": "Q0fomJIDoQ", "executionTime": "1953ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842287810, "type": "tap"}, {"action_id": "y4i304JeJj", "executionTime": "2732ms", "text_to_find": "Move", "timeout": 30, "timestamp": 1746838675751, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "1578ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746838700277, "type": "tap"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "2559ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "1936ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746838766517, "type": "tap"}, {"action_id": "uOt2cFGhGr", "executionTime": "1976ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746838817503, "type": "waitTill"}, {"action_id": "lWIRxRm6HE", "executionTime": "2423ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"action_id": "jIeR7BPEPu", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4445ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "start_x": 50, "start_y": 70, "timestamp": 1750034297199, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "1647ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 30, "timestamp": 1746102698568, "type": "waitTill", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "2782ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "2175ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "2467ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842232923, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "condition": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "timeout": 10}, "condition_type": "exists", "executionTime": "3208ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "then_action": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "timeout": 10, "type": "tap"}, "threshold": 0.7, "timeout": 10, "timestamp": 1746842287165, "type": "ifElseSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "2741ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746842300771, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "condition": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "timeout": 10}, "condition_type": "exists", "executionTime": "3204ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "then_action": {"locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "method": "locator", "timeout": 10, "type": "tap"}, "threshold": 0.7, "timeout": 10, "timestamp": 1746842396053, "type": "ifElseSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "2930ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746843339902, "type": "tapOnText"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "1952ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "jIeR7BPEPu", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2605ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746836318109, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "2046ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "DGzGrOfbSq", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1750034546732, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7], "executionTime": "2129ms"}, {"action_id": "2L5CHzd7qs", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_NZ_Cleanup_20250628211956.json", "test_case_name": "Kmart_NZ_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "nz.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751112641914, "type": "cleanupSteps"}], "labels": [], "updated": "2025-06-28 22:27:01"}