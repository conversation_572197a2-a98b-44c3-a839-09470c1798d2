{"name": "All Sign ins NZ", "created": "2025-06-28 21:56:32", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "JXFxYCr98V", "executionTime": "3483ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "qA1ap4n1m4", "executionTime": "3921ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "XuLgjNG74w", "executionTime": "1148ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "eJnHS9n9VL", "executionTime": "1495ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "f71lbL6wHw", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 5, "timestamp": *************, "type": "multiStep", "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0}, {"action_id": "F0gZF1jEnT", "executionTime": "1705ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068378268, "type": "tap"}, {"action_id": "GHH3xhNGgr", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2677ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748328002311, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "7691ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746068479716, "type": "tap"}, {"action_id": "IvqPpScAJa", "executionTime": "1712ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068564144, "type": "tap"}, {"action_id": "WlISsMf9QA", "executionTime": "1595ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtLog in\"]", "method": "locator", "timeout": 10, "timestamp": 1746068627313, "type": "tap"}, {"action_id": "rJVGLpLWM3", "executionTime": "1155ms", "function_name": "alert_accept", "timestamp": 1746068653559, "type": "iosFunctions"}, {"action_id": "6mHVWI3j5e", "executionTime": "1620ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 20, "timestamp": 1746068667568, "type": "waitTill"}, {"action_id": "3KXtlKCIes", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 5, "timestamp": 1750399635347, "type": "multiStep", "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0}, {"action_id": "FHRlQXe58T", "executionTime": "1686ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068906050, "type": "tap"}, {"action_id": "quZwUwj3a8", "executionTime": "1309ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068941511, "type": "waitTill"}, {"action_id": "ydRnBBO1vR", "executionTime": "1721ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068987218, "type": "tap"}, {"action_id": "BCM1sS8SGA", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2307ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748328020653, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "A1Wz7p1iVG", "executionTime": "1680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746069019618, "type": "tap"}, {"action_id": "lCSewtjn1z", "executionTime": "3234ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "ydRnBBO1vR", "executionTime": "1669ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "L6wTorOX8B", "double_tap": false, "executionTime": "1763ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "text_to_find": "up", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "XuLgjNG74w", "executionTime": "1149ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "6imys82Dy0", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 5, "timestamp": *************, "type": "multiStep", "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0}, {"action_id": "NurQsFoMkE", "executionTime": "1786ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746078231073, "type": "tap"}, {"action_id": "NQGIFb5O7u", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2339ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748328008378, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "1789ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746078255432, "type": "tap"}, {"action_id": "Vyrkv4wK1v", "duration": 5, "time": 5, "timestamp": 1750155010544, "type": "wait"}, {"type": "cleanupSteps", "timestamp": 1751111789428, "test_case_id": "Kmart_NZ_Cleanup_20250628211956.json", "test_case_name": "Kmart_NZ_Cleanup", "test_case_steps_count": 0, "loading_in_progress": false, "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "nz.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "steps_loaded": true, "display_depth": 0, "action_id": "DsDODm7uZx"}], "labels": [], "updated": "2025-06-28 21:56:32"}