{"name": "NZ- Performance", "created": "2025-06-16 23:12:09", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "OR0SKKnFxy", "executionTime": "3382ms", "package_id": "env[appid]", "timestamp": 1749941251799, "type": "restartApp"}, {"action_id": "6G6P3UE7Uy", "executionTime": "3885ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749941298903, "type": "tapOnText"}, {"action_id": "yNAxs8bgMy", "enter": true, "executionTime": "2224ms", "function_name": "text", "text": "P_43515028", "timestamp": 1749942728175, "type": "iosFunctions"}, {"action_id": "9Jhn4eWZwR", "executionTime": "1658ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 20, "timestamp": 1749941518412, "type": "tap"}, {"action_id": "bQrT7FZsxl", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "13893ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749943154253, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "iDtcdR3nSL", "executionTime": "2034ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Instruction Manual\"]", "method": "locator", "timeout": 10, "timestamp": 1749942971578, "type": "tap"}, {"action_id": "Vy3WZ0LTJF", "executionTime": "4261ms", "text_to_find": "Done", "timeout": 30, "timestamp": 1749943081291, "type": "tapOnText"}, {"action_id": "OR0SKKnFxy", "executionTime": "3220ms", "package_id": "env[appid]", "timestamp": 1749945555174, "type": "restartApp"}, {"action_id": "7xs3GiydGF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749944408274, "type": "tap", "executionTime": "1816ms"}, {"action_id": "6G6P3UE7Uy", "executionTime": "3770ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749944417423, "type": "tapOnText"}, {"action_id": "IL6kON0uQ9", "enter": true, "function_name": "text", "text": "kids toys", "timestamp": 1749944478911, "type": "iosFunctions", "executionTime": "2360ms"}, {"action_id": "aqs7O0Yq2p", "expanded": false, "test_case_id": "Click_Paginations_20250611234450.json", "test_case_name": "Click_Paginations", "test_case_steps_count": 10, "timestamp": 1749954595333, "type": "multiStep"}, {"action_id": "5e4LeoW1YU", "package_id": "env[appid]", "timestamp": 1749958538246, "type": "restartApp", "executionTime": "3430ms"}, {"action_id": "KyyS139agr", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749958565710, "type": "tap", "executionTime": "1709ms"}, {"action_id": "zNRPvs2cC4", "text_to_find": "Toys", "timeout": 30, "timestamp": 1749958583078, "type": "tapOnText", "executionTime": "2541ms"}, {"action_id": "eGQ7VrKUSo", "text_to_find": "Age", "timeout": 30, "timestamp": 1749958601936, "type": "tapOnText", "executionTime": "2583ms"}, {"action_id": "dYEtjrv6lz", "text_to_find": "Months", "timeout": 30, "timestamp": 1749958624780, "type": "tapOnText", "executionTime": "2474ms"}, {"action_id": "NkybTKfs2U", "count": 1, "direction": "up", "duration": 2000, "end_x": 90, "end_y": 50, "interval": 0.5, "start_x": 5, "start_y": 50, "timestamp": 1749958806016, "type": "swipe", "vector_end": [0.9, 0.5], "vector_start": [0.05, 0.5], "executionTime": "3927ms"}, {"action_id": "To7bij5MnF", "count": 1, "direction": "up", "duration": 2000, "end_x": 90, "end_y": 50, "interval": 0.5, "start_x": 5, "start_y": 50, "timestamp": *************, "type": "swipe", "vector_end": [0.9, 0.5], "vector_start": [0.05, 0.5], "executionTime": "3287ms"}, {"action_id": "WEB5St2Mb7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "executionTime": "1603ms"}, {"action_id": "SPE01N6pyp", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "executionTime": "3058ms"}, {"action_id": "kQJbqm7uCi", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions", "executionTime": "1163ms"}, {"action_id": "TV4kJIIV9v", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "executionTime": "2046ms"}, {"action_id": "OUT2ASweb6", "enter": true, "function_name": "text", "text": "env[uname]", "timestamp": *************, "type": "iosFunctions", "executionTime": "2732ms"}, {"action_id": "7g2LmvjtEZ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "executionTime": "2072ms"}, {"action_id": "qkZ5KShdEU", "enter": true, "function_name": "text", "text": "env[pwd]", "timestamp": 1749959325022, "type": "iosFunctions", "executionTime": "2531ms"}, {"action_id": "GTXmST3hEA", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 30, "timestamp": 1749959340082, "type": "exists", "executionTime": "1021ms"}, {"action_id": "ewuLtuqVuo", "text_to_find": "Find", "timeout": 30, "timestamp": 1749959489189, "type": "tapOnText", "executionTime": "3967ms"}, {"action_id": "RuPGkdCdah", "enter": true, "function_name": "text", "text": "enn[cooker-id]", "timestamp": 1749959531162, "type": "iosFunctions", "executionTime": "2151ms"}, {"action_id": "ksCBjJiwHZ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 10, "timestamp": 1749959553346, "type": "waitTill", "executionTime": "1384ms"}, {"action_id": "xmelRkcdVx", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1749959577226, "type": "tap", "executionTime": "1872ms"}, {"action_id": "0bnBNoqPt8", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 10, "timestamp": 1749959593634, "type": "waitTill", "executionTime": "2872ms"}, {"action_id": "Ef6OumM2eS", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1749959612145, "type": "tap", "executionTime": "2257ms"}, {"action_id": "OKCHAK6HCJ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749959627955, "type": "tap", "executionTime": "2251ms"}, {"action_id": "x4Mid4HQ0Z", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959799044, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2], "executionTime": "1655ms"}, {"action_id": "RDQCFIxjA0", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959805826, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2], "executionTime": "1564ms"}, {"action_id": "wguGCt7OoB", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749959833850, "type": "tap", "executionTime": "1487ms"}, {"action_id": "ylslyLAYKb", "count": 1, "direction": "left", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "1702ms"}, {"action_id": "RbD937Xbte", "text_to_find": "out", "timeout": 30, "timestamp": *************, "type": "tapOnText", "executionTime": "2529ms"}, {"action_id": "arH1CZCPXh", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 10, "timestamp": *************, "type": "waitTill", "executionTime": "2307ms"}], "labels": [], "updated": "2025-06-16 23:12:09", "test_case_id": "tc_a30eda23f8e9"}